{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NfwmD35AZyMxKVXBueqi4IIx1bL2pcFQNd7sbxO4Kpk=", "__NEXT_PREVIEW_MODE_ID": "90a492e567b059799e485179b439b5cd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3e203fbf14001bbeef58a549a5e23b53a41e0c58c2c76b65d497a608bab14b72", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b8c699e523b6b23df1aff99dc04bb600069b32e39971cb9beb1a376ce2a4e38a"}}}, "functions": {}, "sortedMiddleware": ["/"]}