{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "viTptApR2aCG8nCKhRfcJqigpk+SYkXj7CFX0UtWc44=", "__NEXT_PREVIEW_MODE_ID": "d40256ea3f580ecf3c3f3e6b891e1a16", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bc21a81ab1f026d38cbad00595f1a3c87b74008954d659a37435a3716c8b6981", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d489845dfe7875b61661902e5c97387bbf0120daab0ab8cd59257602acff80dd"}}}, "functions": {}, "sortedMiddleware": ["/"]}