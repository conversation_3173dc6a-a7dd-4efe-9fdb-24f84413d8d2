import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { notifyMeetingRequest } from "@/lib/notifications"

// Webhook endpoint for Cal.com bookings
// This would sync Cal.com bookings back to our MeetBoard system
export async function POST(request: NextRequest) {
  try {
    const payload = await request.json()
    
    // Verify webhook signature (in production)
    // const signature = request.headers.get('x-cal-signature')
    // if (!verifySignature(payload, signature)) {
    //   return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    // }

    // Cal.com webhook payload structure (example)
    const {
      triggerEvent,
      payload: eventData
    } = payload

    if (triggerEvent === "BOOKING_CREATED") {
      const {
        uid,
        title,
        description,
        startTime,
        endTime,
        attendees,
        organizer,
        metadata
      } = eventData

      // Find the freelancer by Cal.com username or email
      // IMPORTANT: This only works if freelancer used the same Google account for both platforms
      const freelancer = await prisma.freelancerProfile.findFirst({
        where: {
          OR: [
            { calLink: { contains: organizer.username } },
            { user: { email: organizer.email } } // This is the key matching point
          ]
        },
        include: {
          user: true
        }
      })

      console.log(`🔍 Looking for freelancer with Cal.com email: ${organizer.email}`)
      console.log(`🔍 Found freelancer: ${freelancer ? freelancer.user.name : 'None'}`)

      if (!freelancer) {
        console.log("Freelancer not found for Cal.com booking")
        return NextResponse.json({ received: true })
      }

      // Find or create client user
      const clientEmail = attendees[0]?.email
      const clientName = attendees[0]?.name

      if (!clientEmail) {
        console.log("No client email in Cal.com booking")
        return NextResponse.json({ received: true })
      }

      let client = await prisma.user.findUnique({
        where: { email: clientEmail }
      })

      if (!client) {
        // Create a new client user
        client = await prisma.user.create({
          data: {
            name: clientName || "Cal.com Client",
            email: clientEmail,
            role: "CLIENT"
          }
        })
      }

      // Create meeting in our system
      const meeting = await prisma.meeting.create({
        data: {
          clientId: client.id,
          freelancerId: freelancer.userId,
          title: title || "Cal.com Meeting",
          description: description || "Meeting booked via Cal.com",
          scheduledAt: new Date(startTime),
          duration: Math.round((new Date(endTime).getTime() - new Date(startTime).getTime()) / (1000 * 60)),
          status: "PENDING", // Cal.com bookings need freelancer approval
          meetingUrl: metadata?.videoCallUrl || null,
          notes: `Synced from Cal.com booking: ${uid}`
        }
      })

      // Send notification to freelancer for approval
      await notifyMeetingRequest(meeting.id, client.id, freelancer.userId)

      console.log(`✅ Synced Cal.com booking ${uid} to MeetBoard - Status: PENDING (awaiting freelancer approval)`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error("Error processing Cal.com webhook:", error)
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    )
  }
}

// Helper function to verify webhook signature (implement in production)
function verifySignature(payload: any, signature: string | null): boolean {
  // Implement Cal.com webhook signature verification
  // This is crucial for security in production
  return true // For MVP, we'll skip verification
}
