const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugSystem() {
  try {
    console.log('🔍 Checking system state...\n')
    
    // Check users
    const users = await prisma.user.findMany({
      include: {
        freelancerProfile: true
      }
    })
    console.log(`👥 Total users: ${users.length}`)
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - Role: ${user.role}`)
      if (user.freelancerProfile) {
        console.log(`    📋 Freelancer profile: ${user.freelancerProfile.title}`)
        console.log(`    🔗 Cal.com link: ${user.freelancerProfile.calLink || 'Not set'}`)
      }
    })
    
    // Check meetings
    const meetings = await prisma.meeting.findMany({
      include: {
        client: { select: { name: true, email: true } },
        freelancer: { select: { name: true, email: true } }
      }
    })
    console.log(`\n📅 Total meetings: ${meetings.length}`)
    meetings.forEach(meeting => {
      console.log(`  - ${meeting.title} (${meeting.status})`)
      console.log(`    Client: ${meeting.client.name} (${meeting.client.email})`)
      console.log(`    Freelancer: ${meeting.freelancer.name} (${meeting.freelancer.email})`)
      console.log(`    Scheduled: ${meeting.scheduledAt}`)
    })
    
    console.log('\n✅ System check complete')
    
  } catch (error) {
    console.error('❌ Error checking system:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugSystem()
