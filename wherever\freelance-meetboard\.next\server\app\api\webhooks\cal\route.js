/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/webhooks/cal/route";
exports.ids = ["app/api/webhooks/cal/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwebhooks%2Fcal%2Froute&page=%2Fapi%2Fwebhooks%2Fcal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fcal%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwebhooks%2Fcal%2Froute&page=%2Fapi%2Fwebhooks%2Fcal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fcal%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_arkit_Desktop_wherever_freelance_meetboard_src_app_api_webhooks_cal_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/webhooks/cal/route.ts */ \"(rsc)/./src/app/api/webhooks/cal/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/webhooks/cal/route\",\n        pathname: \"/api/webhooks/cal\",\n        filename: \"route\",\n        bundlePath: \"app/api/webhooks/cal/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\wherever\\\\freelance-meetboard\\\\src\\\\app\\\\api\\\\webhooks\\\\cal\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_arkit_Desktop_wherever_freelance_meetboard_src_app_api_webhooks_cal_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/webhooks/cal/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwebhooks%2Fcal%2Froute&page=%2Fapi%2Fwebhooks%2Fcal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fcal%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/webhooks/cal/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/webhooks/cal/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_notifications__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/notifications */ \"(rsc)/./src/lib/notifications.ts\");\n\n\n\n// Webhook endpoint for Cal.com bookings\n// This would sync Cal.com bookings back to our MeetBoard system\nasync function POST(request) {\n    try {\n        const payload = await request.json();\n        // Verify webhook signature (in production)\n        // const signature = request.headers.get('x-cal-signature')\n        // if (!verifySignature(payload, signature)) {\n        //   return NextResponse.json({ error: \"Invalid signature\" }, { status: 401 })\n        // }\n        // Cal.com webhook payload structure (example)\n        const { triggerEvent, payload: eventData } = payload;\n        if (triggerEvent === \"BOOKING_CREATED\") {\n            const { uid, title, description, startTime, endTime, attendees, organizer, metadata } = eventData;\n            // Find the freelancer by Cal.com username or email\n            // IMPORTANT: This only works if freelancer used the same Google account for both platforms\n            const freelancer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.freelancerProfile.findFirst({\n                where: {\n                    OR: [\n                        {\n                            calLink: {\n                                contains: organizer.username\n                            }\n                        },\n                        {\n                            user: {\n                                email: organizer.email\n                            }\n                        } // This is the key matching point\n                    ]\n                },\n                include: {\n                    user: true\n                }\n            });\n            console.log(`🔍 Looking for freelancer with Cal.com email: ${organizer.email}`);\n            console.log(`🔍 Found freelancer: ${freelancer ? freelancer.user.name : 'None'}`);\n            if (!freelancer) {\n                console.log(\"Freelancer not found for Cal.com booking\");\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    received: true\n                });\n            }\n            // Find or create client user\n            const clientEmail = attendees[0]?.email;\n            const clientName = attendees[0]?.name;\n            if (!clientEmail) {\n                console.log(\"No client email in Cal.com booking\");\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    received: true\n                });\n            }\n            let client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n                where: {\n                    email: clientEmail\n                }\n            });\n            if (!client) {\n                // Create a new client user\n                client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.create({\n                    data: {\n                        name: clientName || \"Cal.com Client\",\n                        email: clientEmail,\n                        role: \"CLIENT\"\n                    }\n                });\n            }\n            // Create meeting in our system\n            const meeting = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.meeting.create({\n                data: {\n                    clientId: client.id,\n                    freelancerId: freelancer.userId,\n                    title: title || \"Cal.com Meeting\",\n                    description: description || \"Meeting booked via Cal.com\",\n                    scheduledAt: new Date(startTime),\n                    duration: Math.round((new Date(endTime).getTime() - new Date(startTime).getTime()) / (1000 * 60)),\n                    status: \"PENDING\",\n                    meetingUrl: metadata?.videoCallUrl || null,\n                    notes: `Synced from Cal.com booking: ${uid}`\n                }\n            });\n            // Send notification to freelancer for approval\n            await (0,_lib_notifications__WEBPACK_IMPORTED_MODULE_2__.notifyMeetingRequest)(meeting.id, client.id, freelancer.userId);\n            console.log(`✅ Synced Cal.com booking ${uid} to MeetBoard - Status: PENDING (awaiting freelancer approval)`);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            received: true\n        });\n    } catch (error) {\n        console.error(\"Error processing Cal.com webhook:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Webhook processing failed\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to verify webhook signature (implement in production)\nfunction verifySignature(payload, signature) {\n    // Implement Cal.com webhook signature verification\n    // This is crucial for security in production\n    return true // For MVP, we'll skip verification\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/webhooks/cal/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email.ts":
/*!**************************!*\
  !*** ./src/lib/email.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMeetingConfirmedEmail: () => (/* binding */ createMeetingConfirmedEmail),\n/* harmony export */   createMeetingRequestEmail: () => (/* binding */ createMeetingRequestEmail),\n/* harmony export */   sendEmail: () => (/* binding */ sendEmail)\n/* harmony export */ });\n// Email service for sending notifications\n// In production, integrate with services like SendGrid, Mailgun, or AWS SES\nasync function sendEmail(data) {\n    // For MVP, we'll log the email instead of actually sending\n    // In production, replace this with actual email service\n    console.log('📧 Email would be sent:');\n    console.log('To:', data.to);\n    console.log('Subject:', data.subject);\n    console.log('Content:', data.text || data.html);\n    // Simulate email sending delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return {\n        success: true,\n        messageId: `mock-${Date.now()}`\n    };\n}\nfunction createMeetingRequestEmail(freelancerName, clientName, meetingTitle, meetingDate, meetingDescription) {\n    const subject = `New Meeting Request: ${meetingTitle}`;\n    const html = `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n      <h2 style=\"color: #333;\">New Meeting Request</h2>\n      \n      <p>Hi ${freelancerName},</p>\n      \n      <p>You have received a new meeting request from <strong>${clientName}</strong>.</p>\n      \n      <div style=\"background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n        <h3 style=\"margin-top: 0;\">${meetingTitle}</h3>\n        <p><strong>Requested Date:</strong> ${meetingDate}</p>\n        ${meetingDescription ? `<p><strong>Description:</strong> ${meetingDescription}</p>` : ''}\n        <p><strong>Client:</strong> ${clientName}</p>\n      </div>\n      \n      <p>Please log in to your dashboard to approve or decline this meeting request.</p>\n      \n      <div style=\"margin: 30px 0;\">\n        <a href=\"${process.env.NEXTAUTH_URL}/dashboard/meetings\" \n           style=\"background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n          View Meeting Request\n        </a>\n      </div>\n      \n      <p>Best regards,<br>Freelance MeetBoard Team</p>\n    </div>\n  `;\n    const text = `\n    New Meeting Request\n    \n    Hi ${freelancerName},\n    \n    You have received a new meeting request from ${clientName}.\n    \n    Meeting: ${meetingTitle}\n    Date: ${meetingDate}\n    ${meetingDescription ? `Description: ${meetingDescription}` : ''}\n    \n    Please log in to your dashboard to approve or decline this meeting request.\n    \n    Visit: ${process.env.NEXTAUTH_URL}/dashboard/meetings\n    \n    Best regards,\n    Freelance MeetBoard Team\n  `;\n    return {\n        subject,\n        html,\n        text\n    };\n}\nfunction createMeetingConfirmedEmail(clientName, freelancerName, meetingTitle, meetingDate) {\n    const subject = `Meeting Confirmed: ${meetingTitle}`;\n    const html = `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n      <h2 style=\"color: #28a745;\">Meeting Confirmed! 🎉</h2>\n      \n      <p>Hi ${clientName},</p>\n      \n      <p>Great news! <strong>${freelancerName}</strong> has confirmed your meeting request.</p>\n      \n      <div style=\"background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;\">\n        <h3 style=\"margin-top: 0; color: #28a745;\">${meetingTitle}</h3>\n        <p><strong>Date:</strong> ${meetingDate}</p>\n        <p><strong>Freelancer:</strong> ${freelancerName}</p>\n      </div>\n      \n      <p>You can now access the meeting board to communicate with your freelancer.</p>\n      \n      <div style=\"margin: 30px 0;\">\n        <a href=\"${process.env.NEXTAUTH_URL}/dashboard/meetings\" \n           style=\"background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n          Go to Meeting Board\n        </a>\n      </div>\n      \n      <p>Best regards,<br>Freelance MeetBoard Team</p>\n    </div>\n  `;\n    const text = `\n    Meeting Confirmed!\n    \n    Hi ${clientName},\n    \n    Great news! ${freelancerName} has confirmed your meeting request.\n    \n    Meeting: ${meetingTitle}\n    Date: ${meetingDate}\n    \n    You can now access the meeting board to communicate with your freelancer.\n    \n    Visit: ${process.env.NEXTAUTH_URL}/dashboard/meetings\n    \n    Best regards,\n    Freelance MeetBoard Team\n  `;\n    return {\n        subject,\n        html,\n        text\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/notifications.ts":
/*!**********************************!*\
  !*** ./src/lib/notifications.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotification: () => (/* binding */ createNotification),\n/* harmony export */   notifyMeetingCancelled: () => (/* binding */ notifyMeetingCancelled),\n/* harmony export */   notifyMeetingConfirmed: () => (/* binding */ notifyMeetingConfirmed),\n/* harmony export */   notifyMeetingRequest: () => (/* binding */ notifyMeetingRequest),\n/* harmony export */   notifyNewReview: () => (/* binding */ notifyNewReview),\n/* harmony export */   notifyPaymentReceived: () => (/* binding */ notifyPaymentReceived)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _email__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./email */ \"(rsc)/./src/lib/email.ts\");\n\n\nasync function createNotification(userId, title, message, type) {\n    try {\n        const notification = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.notification.create({\n            data: {\n                userId,\n                title,\n                message,\n                type,\n                isRead: false\n            }\n        });\n        return notification;\n    } catch (error) {\n        console.error(\"Error creating notification:\", error);\n        return null;\n    }\n}\nasync function notifyMeetingRequest(meetingId, clientId, freelancerId) {\n    try {\n        console.log(`🔔 Creating notification for meeting ${meetingId} from client ${clientId} to freelancer ${freelancerId}`);\n        // Get meeting and user details\n        const meeting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.meeting.findUnique({\n            where: {\n                id: meetingId\n            },\n            include: {\n                client: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                freelancer: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        if (!meeting) {\n            console.log(`❌ Meeting ${meetingId} not found`);\n            return;\n        }\n        console.log(`📧 Meeting found: ${meeting.title} - Client: ${meeting.client.name}, Freelancer: ${meeting.freelancer.name}`);\n        // Create in-app notification\n        const notification = await createNotification(freelancerId, \"New Meeting Request\", `${meeting.client.name} has requested a meeting: \"${meeting.title}\"`, \"meeting_request\");\n        console.log(`📱 In-app notification created:`, notification?.id);\n        // Send email notification\n        const emailContent = (0,_email__WEBPACK_IMPORTED_MODULE_1__.createMeetingRequestEmail)(meeting.freelancer.name || \"Freelancer\", meeting.client.name || \"Client\", meeting.title, new Date(meeting.scheduledAt).toLocaleString(), meeting.description || undefined);\n        await (0,_email__WEBPACK_IMPORTED_MODULE_1__.sendEmail)({\n            to: meeting.freelancer.email || \"\",\n            subject: emailContent.subject,\n            html: emailContent.html,\n            text: emailContent.text\n        });\n        console.log(`✅ Meeting request notification sent to ${meeting.freelancer.name}`);\n    } catch (error) {\n        console.error(\"❌ Error sending meeting request notification:\", error);\n    }\n}\nasync function notifyMeetingConfirmed(meetingId, clientId, freelancerId) {\n    try {\n        // Get meeting and user details\n        const meeting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.meeting.findUnique({\n            where: {\n                id: meetingId\n            },\n            include: {\n                client: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                freelancer: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        if (!meeting) return;\n        // Create in-app notification for client\n        await createNotification(clientId, \"Meeting Confirmed\", `${meeting.freelancer.name} has confirmed your meeting: \"${meeting.title}\"`, \"meeting_confirmed\");\n        // Send email notification to client\n        const emailContent = (0,_email__WEBPACK_IMPORTED_MODULE_1__.createMeetingConfirmedEmail)(meeting.client.name || \"Client\", meeting.freelancer.name || \"Freelancer\", meeting.title, new Date(meeting.scheduledAt).toLocaleString());\n        await (0,_email__WEBPACK_IMPORTED_MODULE_1__.sendEmail)({\n            to: meeting.client.email || \"\",\n            subject: emailContent.subject,\n            html: emailContent.html,\n            text: emailContent.text\n        });\n        console.log(`✅ Meeting confirmation notification sent to ${meeting.client.name}`);\n    } catch (error) {\n        console.error(\"Error sending meeting confirmation notification:\", error);\n    }\n}\nasync function notifyMeetingCancelled(meetingId, notifyUserId, cancelledByName) {\n    try {\n        const meeting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.meeting.findUnique({\n            where: {\n                id: meetingId\n            }\n        });\n        if (!meeting) return;\n        await createNotification(notifyUserId, \"Meeting Cancelled\", `${cancelledByName} has cancelled the meeting: \"${meeting.title}\"`, \"meeting_cancelled\");\n        console.log(`✅ Meeting cancellation notification sent`);\n    } catch (error) {\n        console.error(\"Error sending meeting cancellation notification:\", error);\n    }\n}\nasync function notifyPaymentReceived(freelancerId, amount, clientName, meetingTitle) {\n    try {\n        await createNotification(freelancerId, \"Payment Received\", `You received $${amount.toFixed(2)} from ${clientName} for \"${meetingTitle}\"`, \"payment\");\n        console.log(`✅ Payment notification sent to freelancer`);\n    } catch (error) {\n        console.error(\"Error sending payment notification:\", error);\n    }\n}\nasync function notifyNewReview(freelancerId, rating, clientName, meetingTitle) {\n    try {\n        await createNotification(freelancerId, \"New Review Received\", `${clientName} left you a ${rating}-star review for \"${meetingTitle}\"`, \"review\");\n        console.log(`✅ Review notification sent to freelancer`);\n    } catch (error) {\n        console.error(\"Error sending review notification:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/notifications.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYXJraXRcXERlc2t0b3BcXHdoZXJldmVyXFxmcmVlbGFuY2UtbWVldGJvYXJkXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KClcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwebhooks%2Fcal%2Froute&page=%2Fapi%2Fwebhooks%2Fcal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fcal%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cwherever%5Cfreelance-meetboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();