"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function DebugMeetingsPage() {
  const { data: session } = useSession()
  const [meetings, setMeetings] = useState<any[]>([])
  const [allMeetings, setAllMeetings] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const fetchUserMeetings = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/meetings")
      const data = await response.json()
      console.log("User meetings:", data)
      setMeetings(data.meetings || [])
    } catch (error) {
      console.error("Error fetching user meetings:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchAllMeetings = async () => {
    setIsLoading(true)
    try {
      // Direct database query to see all meetings
      const response = await fetch("/api/debug/all-meetings")
      if (response.ok) {
        const data = await response.json()
        console.log("All meetings:", data)
        setAllMeetings(data.meetings || [])
      }
    } catch (error) {
      console.error("Error fetching all meetings:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const createTestMeeting = async () => {
    try {
      const response = await fetch("/api/meetings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          freelancerId: "test-freelancer-id", // You'll need to replace with actual freelancer ID
          title: "Debug Test Meeting",
          description: "This is a test meeting for debugging",
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          duration: 60
        }),
      })

      const data = await response.json()
      console.log("Test meeting creation:", data)
      
      if (response.ok) {
        alert("Test meeting created!")
        fetchUserMeetings()
      } else {
        alert("Failed to create test meeting: " + data.error)
      }
    } catch (error) {
      console.error("Error creating test meeting:", error)
    }
  }

  if (!session) {
    return <div className="p-6">Please sign in to debug meetings</div>
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Debug Meetings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button onClick={fetchUserMeetings} disabled={isLoading}>
              Fetch My Meetings
            </Button>
            <Button onClick={fetchAllMeetings} disabled={isLoading} variant="outline">
              Fetch All Meetings
            </Button>
            <Button onClick={createTestMeeting} variant="secondary">
              Create Test Meeting
            </Button>
          </div>

          <div className="bg-blue-50 p-4 rounded">
            <h3 className="font-medium mb-2">Current User Info:</h3>
            <pre className="text-sm">
              {JSON.stringify({
                name: session.user.name,
                email: session.user.email,
                role: session.user.role,
                id: session.user.id
              }, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Meetings */}
        <Card>
          <CardHeader>
            <CardTitle>My Meetings ({meetings.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {meetings.length === 0 ? (
              <p className="text-muted-foreground">No meetings found for this user</p>
            ) : (
              <div className="space-y-2">
                {meetings.map((meeting) => (
                  <div key={meeting.id} className="p-3 border rounded">
                    <h4 className="font-medium">{meeting.title}</h4>
                    <p className="text-sm text-muted-foreground">
                      Status: {meeting.status} | Duration: {meeting.duration}min
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Client: {meeting.client?.name} | Freelancer: {meeting.freelancer?.name}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* All Meetings */}
        <Card>
          <CardHeader>
            <CardTitle>All Meetings ({allMeetings.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {allMeetings.length === 0 ? (
              <p className="text-muted-foreground">No meetings in database</p>
            ) : (
              <div className="space-y-2">
                {allMeetings.map((meeting) => (
                  <div key={meeting.id} className="p-3 border rounded">
                    <h4 className="font-medium">{meeting.title}</h4>
                    <p className="text-sm text-muted-foreground">
                      Status: {meeting.status} | Duration: {meeting.duration}min
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Client ID: {meeting.clientId} | Freelancer ID: {meeting.freelancerId}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
